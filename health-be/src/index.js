const express = require('express');
const cors = require('cors');
const Docker = require('dockerode');

const app = express();
const port = process.env.PORT || 5001;

// Configurar Docker
const docker = new Docker({ socketPath: '/var/run/docker.sock' });

// Middleware
app.use(cors({
  origin: [
    'https://status.msarknet.me',
    'https://msarknet.me',
    'https://*.msarknet.me',
    'http://localhost:5174', // Para desarrollo local
    'http://localhost:3000'  // Para desarrollo local
  ],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
}));
app.use(express.json());

// Servicios que queremos monitorear (basados en tu docker-compose.yml)
const MONITORED_SERVICES = [
  {
    name: 'Traefik',
    containerName: 'traefik',
    url: 'https://traefik.msarknet.me',
    icon: '⚙️',
    description: 'Panel de control del proxy reverso'
  },
  {
    name: 'Adminer',
    containerName: 'adminer',
    url: 'https://adminer.msarknet.me',
    icon: '🗄️',
    description: 'Administrador de base de datos'
  },
  {
    name: 'MariaDB',
    containerName: 'mariadb',
    url: null, // No tiene URL pública
    icon: '🗃️',
    description: 'Base de datos MariaDB'
  },
  {
    name: 'Cerebro Frontend',
    containerName: 'cerebro-fe',
    url: 'https://msarknet.me',
    icon: '🧠',
    description: 'Frontend React de Cerebro'
  },
  {
    name: 'Cerebro Backend',
    containerName: 'cerebro-be',
    url: 'https://api.msarknet.me',
    icon: '⚡',
    description: 'Backend Node.js de Cerebro'
  }
];

// Función para obtener el estado de un contenedor
async function getContainerStatus(containerName) {
  try {
    const containers = await docker.listContainers({ all: true });
    const container = containers.find(c =>
      c.Names.some(name => name.includes(containerName))
    );

    if (!container) {
      return {
        status: 'not_found',
        state: 'Container not found',
        uptime: null,
        health: 'unknown'
      };
    }

    // Obtener información detallada del contenedor
    const containerInfo = docker.getContainer(container.Id);
    const inspect = await containerInfo.inspect();

    return {
      status: container.State === 'running' ? 'running' : container.State,
      state: container.State,
      uptime: container.Status,
      health: inspect.State.Health ? inspect.State.Health.Status : 'no-healthcheck',
      created: container.Created,
      image: container.Image,
      ports: container.Ports || []
    };
  } catch (error) {
    console.error(`Error getting status for ${containerName}:`, error.message);
    return {
      status: 'error',
      state: 'Error checking container',
      uptime: null,
      health: 'unknown',
      error: error.message
    };
  }
}

// Endpoint raíz - Todo está bien
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'API status OK',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// Endpoint para obtener el estado de todos los servicios
app.get('/api/services/status', async (req, res) => {
  try {
    const servicesStatus = await Promise.all(
      MONITORED_SERVICES.map(async (service) => {
        const containerStatus = await getContainerStatus(service.containerName);

        return {
          ...service,
          ...containerStatus,
          lastChecked: new Date().toISOString()
        };
      })
    );

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      services: servicesStatus
    });
  } catch (error) {
    console.error('Error getting services status:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint para obtener el estado de un servicio específico
app.get('/api/services/:serviceName/status', async (req, res) => {
  try {
    const { serviceName } = req.params;
    const service = MONITORED_SERVICES.find(s =>
      s.name.toLowerCase() === serviceName.toLowerCase() ||
      s.containerName === serviceName
    );

    if (!service) {
      return res.status(404).json({
        success: false,
        error: 'Service not found'
      });
    }

    const containerStatus = await getContainerStatus(service.containerName);

    res.json({
      success: true,
      timestamp: new Date().toISOString(),
      service: {
        ...service,
        ...containerStatus,
        lastChecked: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error(`Error getting status for service ${req.params.serviceName}:`, error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Endpoint de health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Health API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Endpoint para obtener información general de Docker
app.get('/api/docker/info', async (req, res) => {
  try {
    const info = await docker.info();
    const version = await docker.version();

    res.json({
      success: true,
      docker: {
        version: version.Version,
        apiVersion: version.ApiVersion,
        containers: info.Containers,
        containersRunning: info.ContainersRunning,
        containersPaused: info.ContainersPaused,
        containersStopped: info.ContainersStopped,
        images: info.Images
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting Docker info:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Manejo de errores
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    timestamp: new Date().toISOString()
  });
});

// Iniciar servidor
app.listen(port, '0.0.0.0', () => {
  console.log(`Health API running on port ${port}`);
});
